# 🔴 Cloudflare Stream Livestream Viewer

Ứng dụng React đơn giản để xem livestream từ Cloudflare Stream với tính năng hiển thị danh sách video đang live và ready.

## ✨ Tính năng

- 🔴 **Xem livestream**: Hi<PERSON>n thị video đang phát trực tiếp từ Cloudflare Stream
- 📺 **Grid layout**: Hiển thị nhiều video cùng lúc trong layout responsive
- 🔄 **Auto-refresh**: Tự động cập nhật danh sách video mỗi 30 giây
- 📱 **Responsive**: Hoạt động tốt trên desktop, tablet và mobile
- 🎛️ **Toggle view**: Chuyển đổi giữa "Live & Ready" và "Tất cả video"

## 🎯 Trạng thái video được hỗ trợ

- 🔴 **live-inprogress**: Đang phát trực tiếp
- 🟢 **live**: Live sẵn sàng
- ✅ **ready**: Sẵn sàng xem
- ⏳ **queued**: <PERSON><PERSON> chờ xử lý
- ⚙️ **inprogress**: <PERSON>ang xử lý

## 🚀 Demo

Ứng dụng có thể chạy trực tiếp bằng file `index.html` hoặc `demo.html` mà không cần build.

## 📁 Cấu trúc project

```
cloudflare-view-livestream-demo/
├── react-simple-app/
│   ├── src/
│   │   ├── components/
│   │   │   ├── LivestreamViewer.js
│   │   │   └── LivestreamViewer.css
│   │   ├── App.js
│   │   ├── App.css
│   │   ├── index.js
│   │   └── index.css
│   ├── public/
│   │   └── index.html
│   ├── demo.html              # Demo chạy trực tiếp
│   ├── index.html             # File deploy
│   ├── .htaccess              # Cấu hình CORS
│   ├── package.json
│   └── README.md
├── .gitignore
└── README.md
```

## 🛠️ Cài đặt và chạy

### Cách 1: Chạy trực tiếp (Khuyến nghị)
```bash
# Mở file demo.html hoặc index.html trong trình duyệt
open react-simple-app/demo.html
```

### Cách 2: Chạy với React development server
```bash
cd react-simple-app
npm install
npm start
```

## 🌐 Deploy

### Deploy lên Hostinger:
1. Upload file `index.html` và `.htaccess` vào thư mục `public_html`
2. Truy cập domain của bạn

### Deploy lên Netlify/Vercel:
1. Kéo thả thư mục `react-simple-app` vào Netlify/Vercel
2. Hoặc connect với GitHub repository

## ⚙️ Cấu hình

Cập nhật thông tin Cloudflare trong file:
- `src/components/LivestreamViewer.js` (cho React app)
- `demo.html` hoặc `index.html` (cho standalone app)

```javascript
const ACCOUNT_ID = 'your-account-id';
const API_TOKEN = 'your-api-token';
```

## 🔧 Công nghệ sử dụng

- **React 18**: UI framework
- **Cloudflare Stream API**: Video streaming service
- **CSS Grid**: Responsive layout
- **Fetch API**: HTTP requests
- **React Hooks**: State management

## 📱 Responsive Design

- **Desktop**: Grid 2-3 video mỗi hàng
- **Tablet**: Grid 2 video mỗi hàng
- **Mobile**: 1 video mỗi hàng

## 🔄 Auto-refresh

Ứng dụng tự động kiểm tra video mới mỗi 30 giây và cập nhật danh sách.

## 🎨 UI/UX

- **Modern design**: Gradient background, glass morphism effects
- **Smooth animations**: Hover effects, transitions
- **Loading states**: Spinner và error handling
- **Status indicators**: Icon và text cho từng trạng thái video

## 📄 License

MIT License

## 🤝 Contributing

1. Fork repository
2. Tạo feature branch
3. Commit changes
4. Push to branch
5. Tạo Pull Request

## 📞 Support

Nếu có vấn đề, vui lòng tạo issue trên GitHub repository.
