const express = require('express');
const path = require('path');
const app = express();
const port = process.env.PORT || 3000;

// Serve static files
app.use(express.static(path.join(__dirname, 'public')));
app.use('/images', express.static(path.join(__dirname, 'public/images')));

// Serve the production HTML file
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'index-production.html'));
});

// Handle all other routes (for SPA)
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'index-production.html'));
});

app.listen(port, () => {
  console.log(`🚀 STOP Mobile App đang chạy tại: http://localhost:${port}`);
  console.log(`📱 Mở trên mobile hoặc responsive mode để xem tốt nhất!`);
});
