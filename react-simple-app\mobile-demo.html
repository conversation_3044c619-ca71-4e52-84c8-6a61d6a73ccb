<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <meta name="theme-color" content="#667eea">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <title>STOP - Mobile Demo</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Loading Screen */
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            animation: fadeOut 1s ease-in-out 3s forwards;
        }

        .background-svg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }

        .background-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .loading-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            animation: fadeIn 0.8s ease-in-out;
            position: relative;
            z-index: 2;
        }

        .logo-container {
            margin-bottom: 40px;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .logo-svg {
            max-width: 250px;
            width: 80vw;
            height: auto;
            filter: drop-shadow(0 10px 30px rgba(0, 0, 0, 0.3))
                    drop-shadow(0 0 50px rgba(255, 215, 0, 0.3));
            animation: logoGlow 2s ease-in-out infinite alternate;
        }

        .loading-spinner {
            position: relative;
            width: 60px;
            height: 60px;
            margin-bottom: 30px;
        }

        .spinner-ring {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: 4px solid transparent;
            border-top: 4px solid #FFD700;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        .spinner-ring:nth-child(2) {
            width: 45px;
            height: 45px;
            top: 7.5px;
            left: 7.5px;
            border-top-color: #FFA500;
            animation-duration: 1.5s;
            animation-direction: reverse;
        }

        .spinner-ring:nth-child(3) {
            width: 30px;
            height: 30px;
            top: 15px;
            left: 15px;
            border-top-color: #FF8C00;
            animation-duration: 2s;
        }

        .loading-text {
            color: white;
            font-size: 1rem;
            font-weight: 500;
            opacity: 0.9;
            animation: pulse 2s ease-in-out infinite;
        }

        /* Main App */
        .main-app {
            opacity: 0;
            animation: fadeIn 1s ease-in-out 4s forwards;
            padding: 20px;
            text-align: center;
            color: white;
        }

        .app-nav {
            background: rgba(0, 0, 0, 0.3);
            padding: 12px 15px;
            display: flex;
            justify-content: center;
            gap: 15px;
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: sticky;
            top: 0;
            z-index: 100;
            margin: -20px -20px 20px -20px;
        }

        .nav-button {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.2);
            padding: 8px 16px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: bold;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            min-width: 120px;
        }

        .nav-button:hover, .nav-button.active {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            border-color: #ff6b6b;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }

        .content-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin: 20px 0;
        }

        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeOut {
            to { opacity: 0; visibility: hidden; }
        }

        @keyframes logoGlow {
            from {
                filter: drop-shadow(0 10px 30px rgba(0, 0, 0, 0.3))
                        drop-shadow(0 0 50px rgba(255, 215, 0, 0.3));
                transform: scale(1);
            }
            to {
                filter: drop-shadow(0 15px 40px rgba(0, 0, 0, 0.4))
                        drop-shadow(0 0 80px rgba(255, 215, 0, 0.6));
                transform: scale(1.05);
            }
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.7; }
            50% { opacity: 1; }
        }

        /* Mobile specific */
        @media (max-width: 480px) {
            .logo-text {
                font-size: 2rem;
                letter-spacing: 2px;
            }
            
            .logo {
                padding: 15px 30px;
            }
            
            .nav-button {
                font-size: 0.8rem;
                padding: 6px 12px;
                min-width: 100px;
            }
            
            .content-section {
                padding: 15px;
                margin: 15px 0;
            }
        }
    </style>
</head>
<body>
    <!-- Loading Screen -->
    <div class="loading-screen">
        <div class="loading-content">
            <div class="logo-container">
                <div class="logo">
                    <span class="logo-text">STOP</span>
                    <div class="logo-wings">
                        <div class="wing wing-left"></div>
                        <div class="wing wing-right"></div>
                    </div>
                </div>
            </div>
            
            <div class="loading-spinner">
                <div class="spinner-ring"></div>
                <div class="spinner-ring"></div>
                <div class="spinner-ring"></div>
            </div>
            
            <div class="loading-text">
                <p>Đang tải...</p>
            </div>
        </div>
    </div>

    <!-- Main App -->
    <div class="main-app">
        <nav class="app-nav">
            <button class="nav-button active">🏠 Trang chủ</button>
            <button class="nav-button">🔴 Livestream</button>
        </nav>

        <div class="content-section">
            <h1>🚀 STOP Mobile App</h1>
            <p>Chào mừng bạn đến với ứng dụng livestream mobile!</p>
        </div>

        <div class="content-section">
            <h2>📱 Tối ưu cho Mobile</h2>
            <p>Giao diện đã được tối ưu hóa cho thiết bị di động với:</p>
            <ul style="text-align: left; max-width: 300px; margin: 15px auto;">
                <li>✅ Responsive design</li>
                <li>✅ Touch-friendly buttons</li>
                <li>✅ Mobile viewport</li>
                <li>✅ Loading screen với logo STOP</li>
            </ul>
        </div>
    </div>
</body>
</html>
