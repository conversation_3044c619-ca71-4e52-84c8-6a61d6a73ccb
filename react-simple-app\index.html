<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>React Simple App - Demo</title>
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
                'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
                sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        * {
            box-sizing: border-box;
        }

        .App {
            text-align: center;
        }

        .App-nav {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px 20px;
            display: flex;
            justify-content: center;
            gap: 20px;
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .App-nav button {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.2);
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: bold;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .App-nav button:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .App-nav button.active {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            border-color: #ff6b6b;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }

        .App-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 40px 20px;
            color: white;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-size: calc(10px + 2vmin);
        }

        .App-header h1 {
            margin-bottom: 20px;
            font-size: 2.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .App-header p {
            font-size: 1.2rem;
            margin-bottom: 30px;
            opacity: 0.9;
        }

        .counter-section, .input-section, .info-section {
            margin: 30px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 500px;
            width: 100%;
        }

        .counter-section h2 {
            font-size: 1.8rem;
            margin-bottom: 20px;
            color: #fff;
        }

        .button-group {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .button-group button {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .button-group button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
            background: linear-gradient(45deg, #ee5a24, #ff6b6b);
        }

        .button-group button:active {
            transform: translateY(0);
        }

        .input-section h2 {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: #fff;
        }

        .input-section input {
            padding: 12px 20px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            width: 250px;
            max-width: 100%;
            text-align: center;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .input-section input:focus {
            outline: none;
            box-shadow: 0 6px 20px rgba(0,0,0,0.2);
            transform: scale(1.05);
        }

        .input-section input::placeholder {
            color: #666;
        }

        .info-section {
            font-size: 1rem;
        }

        .info-section p {
            margin-bottom: 15px;
            font-size: 1.1rem;
        }

        .info-section ul {
            list-style: none;
            padding: 0;
            text-align: left;
            max-width: 300px;
            margin: 0 auto;
        }

        .info-section li {
            padding: 8px 0;
            font-size: 1rem;
            opacity: 0.9;
        }

        @media (max-width: 768px) {
            .App-header {
                padding: 20px 10px;
                font-size: calc(8px + 2vmin);
            }
            
            .App-header h1 {
                font-size: 2rem;
            }
            
            .button-group {
                flex-direction: column;
                align-items: center;
            }
            
            .button-group button {
                width: 200px;
            }
            
            .input-section input {
                width: 200px;
            }
        }

        /* Livestream Styles */
        .livestream-container {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .livestream-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .livestream-header h1 {
            font-size: 2rem;
            margin: 0;
            color: #fff;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header-controls {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .refresh-button, .retry-button, .toggle-button {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .refresh-button:hover, .retry-button:hover, .toggle-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
            background: linear-gradient(45deg, #ee5a24, #ff6b6b);
        }

        .toggle-button {
            background: linear-gradient(45deg, #667eea, #764ba2);
        }

        .toggle-button.active {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
        }

        .toggle-button:hover {
            background: linear-gradient(45deg, #764ba2, #667eea);
        }

        .toggle-button.active:hover {
            background: linear-gradient(45deg, #ee5a24, #ff6b6b);
        }

        .loading-state {
            text-align: center;
            padding: 60px 20px;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid #fff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-state p {
            font-size: 1.2rem;
            color: #fff;
            opacity: 0.9;
        }

        .error-state, .no-streams-state {
            text-align: center;
            padding: 60px 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .error-state h2 {
            color: #ff6b6b;
            margin-bottom: 15px;
        }

        .error-state p, .no-streams-state p {
            color: #fff;
            margin-bottom: 25px;
            opacity: 0.9;
        }

        .no-streams-state h2 {
            color: #fff;
            margin-bottom: 15px;
        }

        .video-player-container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .video-info {
            margin-bottom: 20px;
            text-align: center;
        }

        .video-info h2 {
            color: #fff;
            margin-bottom: 10px;
            font-size: 1.5rem;
        }

        .video-info p {
            color: #fff;
            opacity: 0.8;
            font-size: 0.9rem;
        }

        .video-wrapper {
            position: relative;
            width: 100%;
            height: 0;
            padding-bottom: 56.25%;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }

        /* Videos Grid */
        .videos-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-top: 20px;
        }

        .video-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .video-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            background: rgba(255, 255, 255, 0.15);
        }

        .video-player-container {
            width: 100%;
        }

        .video-info {
            margin-bottom: 15px;
            text-align: left;
        }

        .video-info h3 {
            color: #fff;
            margin-bottom: 8px;
            font-size: 1.2rem;
            font-weight: bold;
        }

        .video-info p {
            color: #fff;
            opacity: 0.8;
            font-size: 0.85rem;
            margin-bottom: 4px;
        }

        @media (max-width: 768px) {
            .videos-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .video-card {
                padding: 15px;
            }

            .video-info h3 {
                font-size: 1.1rem;
            }
        }

        .stream-status {
            font-size: 0.9rem;
            opacity: 0.9;
            margin-bottom: 5px;
        }

        .stream-duration {
            font-size: 0.8rem;
            opacity: 0.7;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        function LivestreamViewer() {
            const [liveStreams, setLiveStreams] = React.useState([]);
            const [selectedStream, setSelectedStream] = React.useState(null);
            const [loading, setLoading] = React.useState(true);
            const [error, setError] = React.useState(null);
            const [showAllVideos, setShowAllVideos] = React.useState(false);

            const ACCOUNT_ID = '81e895f7d306ee24edc4f8aee29129e6';
            const API_TOKEN = 'PiwBmbEM6Jh1oQu94HtfXOsy9ADchmZTPELWhBkw';

            const getVideoStatus = (video) => {
                if (video.status?.state === 'live-inprogress') {
                    return '🔴 Đang phát trực tiếp';
                } else if (video.status?.state === 'live') {
                    return '🟢 Live sẵn sàng';
                } else if (video.status?.state === 'ready') {
                    return '✅ Sẵn sàng xem';
                } else if (video.status?.state === 'queued') {
                    return '⏳ Đang chờ xử lý';
                } else if (video.status?.state === 'inprogress') {
                    return '⚙️ Đang xử lý';
                } else if (video.live === true) {
                    return '🔴 Live';
                } else if (video.liveInput && video.liveInput.status === 'connected') {
                    return '🟢 Kết nối live';
                } else {
                    return '⏸️ Đã ghi';
                }
            };

            const fetchLiveVideos = async () => {
                try {
                    setLoading(true);
                    setError(null);

                    const response = await fetch(
                        `https://api.cloudflare.com/client/v4/accounts/${ACCOUNT_ID}/stream`,
                        {
                            method: 'GET',
                            headers: {
                                'Authorization': `Bearer ${API_TOKEN}`,
                                'Content-Type': 'application/json',
                            },
                        }
                    );

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const data = await response.json();

                    if (data.success) {
                        const filteredVideos = data.result.filter(video => {
                            if (showAllVideos) {
                                return video.live === true ||
                                       video.status?.state === 'live' ||
                                       video.status?.state === 'live-inprogress' ||
                                       video.status?.state === 'ready' ||
                                       video.status?.state === 'queued' ||
                                       video.status?.state === 'inprogress' ||
                                       (video.liveInput && video.liveInput.status === 'connected');
                            } else {
                                return video.live === true ||
                                       video.status?.state === 'live' ||
                                       video.status?.state === 'live-inprogress' ||
                                       video.status?.state === 'ready' ||
                                       (video.liveInput && video.liveInput.status === 'connected');
                            }
                        });

                        setLiveStreams(filteredVideos);

                        if (filteredVideos.length > 0 && !selectedStream) {
                            setSelectedStream(filteredVideos[0]);
                        }
                    } else {
                        throw new Error(data.errors?.[0]?.message || 'Failed to fetch videos');
                    }
                } catch (err) {
                    console.error('Error fetching live videos:', err);
                    setError(err.message);
                } finally {
                    setLoading(false);
                }
            };

            React.useEffect(() => {
                fetchLiveVideos();
                const interval = setInterval(fetchLiveVideos, 30000);
                return () => clearInterval(interval);
            }, [showAllVideos]);

            if (loading && liveStreams.length === 0) {
                return React.createElement('div', { className: 'livestream-container' },
                    React.createElement('div', { className: 'loading-state' },
                        React.createElement('div', { className: 'spinner' }),
                        React.createElement('p', null, 'Đang tìm kiếm livestream...')
                    )
                );
            }

            if (error) {
                return React.createElement('div', { className: 'livestream-container' },
                    React.createElement('div', { className: 'error-state' },
                        React.createElement('h2', null, '❌ Lỗi kết nối'),
                        React.createElement('p', null, error),
                        React.createElement('button', {
                            onClick: fetchLiveVideos,
                            className: 'retry-button'
                        }, '🔄 Thử lại')
                    )
                );
            }

            if (liveStreams.length === 0) {
                return React.createElement('div', { className: 'livestream-container' },
                    React.createElement('div', { className: 'no-streams-state' },
                        React.createElement('h2', null, '📺 Không có livestream nào đang phát'),
                        React.createElement('p', null, 'Hiện tại không có livestream nào đang được phát trực tiếp.'),
                        React.createElement('button', {
                            onClick: fetchLiveVideos,
                            className: 'refresh-button'
                        }, '🔄 Làm mới')
                    )
                );
            }

            return React.createElement('div', { className: 'livestream-container' },
                React.createElement('div', { className: 'livestream-header' },
                    React.createElement('h1', null, showAllVideos ? '📺 Tất cả video' : '🔴 Live & Ready Videos'),
                    React.createElement('div', { className: 'header-controls' },
                        React.createElement('button', {
                            onClick: () => setShowAllVideos(!showAllVideos),
                            className: `toggle-button ${showAllVideos ? 'active' : ''}`
                        }, showAllVideos ? '🔴 Live & Ready' : '📺 Tất cả video'),
                        React.createElement('button', {
                            onClick: fetchLiveVideos,
                            className: 'refresh-button'
                        }, `🔄 Làm mới (${liveStreams.length})`)
                    )
                ),
                React.createElement('div', { className: 'videos-grid' },
                    liveStreams.map(stream =>
                        React.createElement('div', { key: stream.uid, className: 'video-card' },
                            React.createElement('div', { className: 'video-player-container' },
                                React.createElement('div', { className: 'video-info' },
                                    React.createElement('h3', null, stream.meta?.name || stream.filename || `Video ${stream.uid.substring(0, 8)}`),
                                    React.createElement('p', null, `Video ID: ${stream.uid}`),
                                    React.createElement('p', null, `Status: ${getVideoStatus(stream)}`),
                                    stream.created && React.createElement('p', null, `Created: ${new Date(stream.created).toLocaleString()}`),
                                    stream.duration && React.createElement('p', null, `Duration: ${Math.floor(stream.duration)}s`)
                                ),
                                React.createElement('div', { className: 'video-wrapper' },
                                    React.createElement('iframe', {
                                        src: `https://iframe.videodelivery.net/${stream.uid}`,
                                        style: {
                                            border: 'none',
                                            position: 'absolute',
                                            top: 0,
                                            left: 0,
                                            height: '100%',
                                            width: '100%',
                                        },
                                        allow: 'accelerometer; gyroscope; autoplay; encrypted-media; picture-in-picture;',
                                        allowFullScreen: true,
                                        title: `Cloudflare Stream Player - ${stream.meta?.name || stream.filename || stream.uid}`
                                    })
                                )
                            )
                        )
                    )
                )
            );
        }

        function App() {
            const [currentView, setCurrentView] = React.useState('home');
            const [count, setCount] = React.useState(0);
            const [name, setName] = React.useState('');

            const renderView = () => {
                switch(currentView) {
                    case 'livestream':
                        return React.createElement(LivestreamViewer);
                    case 'home':
                    default:
                        return React.createElement(React.Fragment, null,
                            React.createElement('h1', null, '🚀 React Simple App'),
                            React.createElement('p', null, 'Chào mừng bạn đến với ứng dụng React đơn giản!'),

                            React.createElement('div', { className: 'counter-section' },
                                React.createElement('h2', null, `Bộ đếm: ${count}`),
                                React.createElement('div', { className: 'button-group' },
                                    React.createElement('button', {
                                        onClick: () => setCount(count + 1)
                                    }, 'Tăng (+)'),
                                    React.createElement('button', {
                                        onClick: () => setCount(count - 1)
                                    }, 'Giảm (-)'),
                                    React.createElement('button', {
                                        onClick: () => setCount(0)
                                    }, 'Reset')
                                )
                            ),

                            React.createElement('div', { className: 'input-section' },
                                React.createElement('h2', null, `Xin chào, ${name || 'Bạn'}!`),
                                React.createElement('input', {
                                    type: 'text',
                                    placeholder: 'Nhập tên của bạn...',
                                    value: name,
                                    onChange: (e) => setName(e.target.value)
                                })
                            ),

                            React.createElement('div', { className: 'info-section' },
                                React.createElement('p', null, 'Đây là một ứng dụng React đơn giản với:'),
                                React.createElement('ul', null,
                                    React.createElement('li', null, '✅ Bộ đếm với useState'),
                                    React.createElement('li', null, '✅ Input form với state'),
                                    React.createElement('li', null, '✅ CSS styling'),
                                    React.createElement('li', null, '✅ Responsive design'),
                                    React.createElement('li', null, '🔴 Xem Livestream với Cloudflare')
                                )
                            )
                        );
                }
            };

            return React.createElement('div', { className: 'App' },
                React.createElement('nav', { className: 'App-nav' },
                    React.createElement('button', {
                        className: currentView === 'home' ? 'active' : '',
                        onClick: () => setCurrentView('home')
                    }, '🏠 Trang chủ'),
                    React.createElement('button', {
                        className: currentView === 'livestream' ? 'active' : '',
                        onClick: () => setCurrentView('livestream')
                    }, '🔴 Livestream')
                ),
                React.createElement('header', { className: 'App-header' },
                    renderView()
                )
            );
        }

        const root = ReactDOM.createRoot(document.getElementById('root'));
        root.render(<App />);
    </script>
</body>
</html>
