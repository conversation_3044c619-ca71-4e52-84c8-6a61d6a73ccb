<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <meta name="theme-color" content="#667eea">
    <title>STOP - Livestream Demo</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .controls {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .btn {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.2);
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: bold;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .btn:hover {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            border-color: #ff6b6b;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }

        .status {
            text-align: center;
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }

        .videos-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .video-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .video-info {
            margin-bottom: 15px;
        }

        .video-info h3 {
            font-size: 1.3rem;
            margin-bottom: 10px;
            color: #FFD700;
        }

        .video-info p {
            margin: 5px 0;
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .video-wrapper {
            position: relative;
            width: 100%;
            height: 250px;
            background: #000;
            border-radius: 10px;
            overflow: hidden;
        }

        .video-wrapper iframe {
            width: 100%;
            height: 100%;
            border: none;
        }

        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error {
            color: #ff6b6b;
            text-align: center;
            padding: 20px;
        }

        .no-videos {
            text-align: center;
            padding: 40px;
            opacity: 0.8;
        }

        /* Mobile responsive */
        @media (max-width: 768px) {
            .header h1 { font-size: 2rem; }
            .videos-grid { 
                grid-template-columns: 1fr;
                gap: 15px;
            }
            .video-card { padding: 15px; }
            .video-wrapper { height: 200px; }
            .controls { gap: 10px; }
            .btn { 
                padding: 10px 20px;
                font-size: 0.9rem;
            }
        }

        @media (max-width: 480px) {
            body { padding: 10px; }
            .header h1 { font-size: 1.5rem; }
            .video-wrapper { height: 180px; }
            .btn { 
                padding: 8px 16px;
                font-size: 0.8rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔴 STOP Livestream</h1>
            <p>Xem livestream từ Cloudflare Stream</p>
        </div>

        <div class="controls">
            <button class="btn" onclick="fetchStreams()">🔄 Tải lại</button>
            <button class="btn" onclick="toggleShowAll()">📺 <span id="toggle-text">Hiện tất cả</span></button>
        </div>

        <div class="status" id="status">
            <div class="loading">
                <div class="spinner"></div>
                <span>Đang tải livestream...</span>
            </div>
        </div>

        <div class="videos-grid" id="videos-grid"></div>
    </div>

    <script>
        // Cloudflare credentials
        const ACCOUNT_ID = '81e895f7d306ee24edc4f8aee29129e6';
        const API_TOKEN = 'PiwBmbEM6Jh1oQu94HtfXOsy9ADchmZTPELWhBkw';
        
        let showAllVideos = false;
        let allVideos = [];

        // Get video status
        function getVideoStatus(video) {
            if (video.status?.state === 'live-inprogress') {
                return '🔴 Đang phát trực tiếp';
            } else if (video.status?.state === 'live') {
                return '🟢 Live sẵn sàng';
            } else if (video.status?.state === 'ready') {
                return '✅ Sẵn sàng xem';
            } else if (video.status?.state === 'queued') {
                return '⏳ Đang chờ xử lý';
            } else if (video.status?.state === 'inprogress') {
                return '⚙️ Đang xử lý';
            } else if (video.live === true) {
                return '🔴 Live';
            } else if (video.liveInput && video.liveInput.status === 'connected') {
                return '🟢 Kết nối live';
            } else {
                return '⏸️ Đã ghi';
            }
        }

        // Fetch streams from Cloudflare
        async function fetchStreams() {
            const statusEl = document.getElementById('status');
            const videosEl = document.getElementById('videos-grid');
            
            statusEl.innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    <span>Đang tải livestream từ Cloudflare...</span>
                </div>
            `;
            videosEl.innerHTML = '';

            try {
                const response = await fetch(
                    `https://api.cloudflare.com/client/v4/accounts/${ACCOUNT_ID}/stream`,
                    {
                        method: 'GET',
                        headers: {
                            'Authorization': `Bearer ${API_TOKEN}`,
                            'Content-Type': 'application/json',
                        },
                    }
                );

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                if (data.success) {
                    allVideos = data.result;
                    displayVideos();
                    statusEl.innerHTML = `✅ Đã tải ${allVideos.length} video thành công!`;
                } else {
                    throw new Error(data.errors?.[0]?.message || 'Failed to fetch videos');
                }
            } catch (error) {
                console.error('Error:', error);
                statusEl.innerHTML = `
                    <div class="error">
                        ❌ Lỗi: ${error.message}<br>
                        <small>Có thể do CORS policy. Hãy thử chạy từ server hoặc dùng React app.</small>
                    </div>
                `;
            }
        }

        // Display videos
        function displayVideos() {
            const videosEl = document.getElementById('videos-grid');
            
            // Filter videos
            const filteredVideos = allVideos.filter(video => {
                if (showAllVideos) {
                    return true;
                } else {
                    return video.live === true ||
                           video.status?.state === 'live' ||
                           video.status?.state === 'live-inprogress' ||
                           video.status?.state === 'ready' ||
                           (video.liveInput && video.liveInput.status === 'connected');
                }
            });

            if (filteredVideos.length === 0) {
                videosEl.innerHTML = `
                    <div class="no-videos">
                        <h2>📺 Không có video nào</h2>
                        <p>${showAllVideos ? 'Không có video nào trong tài khoản.' : 'Không có livestream nào đang phát.'}</p>
                    </div>
                `;
                return;
            }

            videosEl.innerHTML = filteredVideos.map(video => `
                <div class="video-card">
                    <div class="video-info">
                        <h3>${video.meta?.name || video.filename || `Video ${video.uid.substring(0, 8)}`}</h3>
                        <p><strong>ID:</strong> ${video.uid}</p>
                        <p><strong>Trạng thái:</strong> ${getVideoStatus(video)}</p>
                        ${video.created ? `<p><strong>Tạo:</strong> ${new Date(video.created).toLocaleString()}</p>` : ''}
                        ${video.duration ? `<p><strong>Thời lượng:</strong> ${Math.floor(video.duration)}s</p>` : ''}
                    </div>
                    <div class="video-wrapper">
                        <iframe
                            src="https://iframe.videodelivery.net/${video.uid}"
                            allow="accelerometer; gyroscope; autoplay; encrypted-media; picture-in-picture;"
                            allowfullscreen
                            title="Cloudflare Stream Player - ${video.meta?.name || video.filename || video.uid}">
                        </iframe>
                    </div>
                </div>
            `).join('');
        }

        // Toggle show all videos
        function toggleShowAll() {
            showAllVideos = !showAllVideos;
            document.getElementById('toggle-text').textContent = showAllVideos ? 'Chỉ Live' : 'Hiện tất cả';
            displayVideos();
        }

        // Auto-refresh every 30 seconds
        setInterval(fetchStreams, 30000);

        // Initial load
        fetchStreams();
    </script>
</body>
</html>
