.App {
  text-align: center;
}

.App-nav {
  background: rgba(0, 0, 0, 0.3);
  padding: 15px 20px;
  display: flex;
  justify-content: center;
  gap: 20px;
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.App-nav button {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.2);
  padding: 10px 20px;
  border-radius: 25px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: bold;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.App-nav button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.App-nav button.active {
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  border-color: #ff6b6b;
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.App-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40px 20px;
  color: white;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
}

.App-header h1 {
  margin-bottom: 20px;
  font-size: 2.5rem;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.App-header p {
  font-size: 1.2rem;
  margin-bottom: 30px;
  opacity: 0.9;
}

.counter-section, .input-section, .info-section {
  margin: 30px 0;
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  max-width: 500px;
  width: 100%;
}

.counter-section h2 {
  font-size: 1.8rem;
  margin-bottom: 20px;
  color: #fff;
}

.button-group {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

.button-group button {
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 25px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: bold;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.button-group button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0,0,0,0.3);
  background: linear-gradient(45deg, #ee5a24, #ff6b6b);
}

.button-group button:active {
  transform: translateY(0);
}

.input-section h2 {
  font-size: 1.5rem;
  margin-bottom: 15px;
  color: #fff;
}

.input-section input {
  padding: 12px 20px;
  border: none;
  border-radius: 25px;
  font-size: 1rem;
  width: 250px;
  max-width: 100%;
  text-align: center;
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.input-section input:focus {
  outline: none;
  box-shadow: 0 6px 20px rgba(0,0,0,0.2);
  transform: scale(1.05);
}

.input-section input::placeholder {
  color: #666;
}

.info-section {
  font-size: 1rem;
}

.info-section p {
  margin-bottom: 15px;
  font-size: 1.1rem;
}

.info-section ul {
  list-style: none;
  padding: 0;
  text-align: left;
  max-width: 300px;
  margin: 0 auto;
}

.info-section li {
  padding: 8px 0;
  font-size: 1rem;
  opacity: 0.9;
}

@media (max-width: 768px) {
  .App-header {
    padding: 20px 10px;
    font-size: calc(8px + 2vmin);
  }
  
  .App-header h1 {
    font-size: 2rem;
  }
  
  .button-group {
    flex-direction: column;
    align-items: center;
  }
  
  .button-group button {
    width: 200px;
  }
  
  .input-section input {
    width: 200px;
  }
}
