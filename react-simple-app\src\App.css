.App {
  text-align: center;
  min-height: 100vh;
  width: 100%;
  overflow-x: hidden;
}

.App-nav {
  background: rgba(0, 0, 0, 0.3);
  padding: 15px 20px;
  display: flex;
  justify-content: center;
  gap: 20px;
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
  width: 100%;
  box-sizing: border-box;
}

.App-nav button {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.2);
  padding: 10px 20px;
  border-radius: 25px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: bold;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.App-nav button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.App-nav button.active {
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  border-color: #ff6b6b;
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.App-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px 15px;
  color: white;
  min-height: calc(100vh - 70px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  font-size: calc(10px + 2vmin);
  width: 100%;
  box-sizing: border-box;
  padding-top: 30px;
}

.App-header h1 {
  margin-bottom: 20px;
  font-size: 2.5rem;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.App-header p {
  font-size: 1.2rem;
  margin-bottom: 30px;
  opacity: 0.9;
}

.counter-section, .input-section, .info-section {
  margin: 30px 0;
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  max-width: 500px;
  width: 100%;
}

.counter-section h2 {
  font-size: 1.8rem;
  margin-bottom: 20px;
  color: #fff;
}

.button-group {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

.button-group button {
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 25px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: bold;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.button-group button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0,0,0,0.3);
  background: linear-gradient(45deg, #ee5a24, #ff6b6b);
}

.button-group button:active {
  transform: translateY(0);
}

.input-section h2 {
  font-size: 1.5rem;
  margin-bottom: 15px;
  color: #fff;
}

.input-section input {
  padding: 12px 20px;
  border: none;
  border-radius: 25px;
  font-size: 1rem;
  width: 250px;
  max-width: 100%;
  text-align: center;
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.input-section input:focus {
  outline: none;
  box-shadow: 0 6px 20px rgba(0,0,0,0.2);
  transform: scale(1.05);
}

.input-section input::placeholder {
  color: #666;
}

.info-section {
  font-size: 1rem;
}

.info-section p {
  margin-bottom: 15px;
  font-size: 1.1rem;
}

.info-section ul {
  list-style: none;
  padding: 0;
  text-align: left;
  max-width: 300px;
  margin: 0 auto;
}

.info-section li {
  padding: 8px 0;
  font-size: 1rem;
  opacity: 0.9;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .App-nav {
    padding: 12px 15px;
    gap: 15px;
    flex-wrap: wrap;
  }

  .App-nav button {
    padding: 8px 16px;
    font-size: 0.9rem;
    min-width: 120px;
  }

  .App-header {
    padding: 15px 10px;
    font-size: calc(8px + 2vmin);
    min-height: calc(100vh - 60px);
    padding-top: 20px;
  }

  .App-header h1 {
    font-size: 1.8rem;
    margin-bottom: 15px;
  }

  .App-header p {
    font-size: 1rem;
    margin-bottom: 20px;
  }

  .counter-section, .input-section, .info-section {
    margin: 20px 0;
    padding: 15px;
    max-width: 90%;
  }

  .button-group {
    flex-direction: column;
    align-items: center;
    gap: 10px;
  }

  .button-group button {
    width: 180px;
    padding: 10px 20px;
    font-size: 0.9rem;
  }

  .input-section input {
    width: 180px;
    padding: 10px 15px;
    font-size: 0.9rem;
  }

  .info-section ul {
    max-width: 250px;
  }

  .info-section li {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .App-nav {
    padding: 10px 12px;
    gap: 10px;
  }

  .App-nav button {
    padding: 6px 12px;
    font-size: 0.8rem;
    min-width: 100px;
  }

  .App-header {
    padding: 10px 8px;
    padding-top: 15px;
  }

  .App-header h1 {
    font-size: 1.5rem;
    margin-bottom: 10px;
  }

  .App-header p {
    font-size: 0.9rem;
    margin-bottom: 15px;
  }

  .counter-section, .input-section, .info-section {
    margin: 15px 0;
    padding: 12px;
    max-width: 95%;
  }

  .counter-section h2 {
    font-size: 1.4rem;
  }

  .input-section h2 {
    font-size: 1.2rem;
  }

  .button-group button {
    width: 160px;
    padding: 8px 16px;
    font-size: 0.8rem;
  }

  .input-section input {
    width: 160px;
    padding: 8px 12px;
    font-size: 0.8rem;
  }

  .info-section {
    font-size: 0.9rem;
  }

  .info-section p {
    font-size: 1rem;
  }

  .info-section li {
    font-size: 0.8rem;
    padding: 6px 0;
  }
}
