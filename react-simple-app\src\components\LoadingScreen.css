.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  overflow: hidden;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  animation: fadeIn 0.8s ease-in-out;
}

.logo-container {
  margin-bottom: 40px;
  position: relative;
}

.logo {
  position: relative;
  display: inline-block;
  padding: 20px 40px;
  background: linear-gradient(45deg, #FFD700, #FFA500);
  border-radius: 50px;
  box-shadow: 
    0 10px 30px rgba(0, 0, 0, 0.3),
    0 0 50px rgba(255, 215, 0, 0.3),
    inset 0 2px 10px rgba(255, 255, 255, 0.3);
  animation: logoGlow 2s ease-in-out infinite alternate;
}

.logo-text {
  font-family: '<PERSON><PERSON>', <PERSON><PERSON>, sans-serif;
  font-size: 3rem;
  font-weight: 900;
  color: #1e3c72;
  text-shadow: 
    2px 2px 4px rgba(0, 0, 0, 0.3),
    0 0 10px rgba(255, 255, 255, 0.5);
  letter-spacing: 3px;
  position: relative;
  z-index: 2;
}

.logo-wings {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 80px;
  z-index: 1;
}

.wing {
  position: absolute;
  top: 50%;
  width: 60px;
  height: 30px;
  background: linear-gradient(45deg, #FFD700, #FFA500);
  border-radius: 50px 10px 50px 10px;
  transform: translateY(-50%);
  animation: wingFlap 1.5s ease-in-out infinite alternate;
}

.wing-left {
  left: -30px;
  transform: translateY(-50%) rotate(-15deg);
  border-radius: 50px 10px 50px 10px;
}

.wing-right {
  right: -30px;
  transform: translateY(-50%) rotate(15deg);
  border-radius: 10px 50px 10px 50px;
}

.loading-spinner {
  position: relative;
  width: 80px;
  height: 80px;
  margin-bottom: 30px;
}

.spinner-ring {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 4px solid transparent;
  border-top: 4px solid #FFD700;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.spinner-ring:nth-child(2) {
  width: 60px;
  height: 60px;
  top: 10px;
  left: 10px;
  border-top-color: #FFA500;
  animation-duration: 1.5s;
  animation-direction: reverse;
}

.spinner-ring:nth-child(3) {
  width: 40px;
  height: 40px;
  top: 20px;
  left: 20px;
  border-top-color: #FF8C00;
  animation-duration: 2s;
}

.loading-text {
  color: white;
  font-size: 1.2rem;
  font-weight: 500;
  opacity: 0.9;
  animation: pulse 2s ease-in-out infinite;
}

.loading-text p {
  margin: 0;
  letter-spacing: 1px;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes logoGlow {
  from {
    box-shadow: 
      0 10px 30px rgba(0, 0, 0, 0.3),
      0 0 50px rgba(255, 215, 0, 0.3),
      inset 0 2px 10px rgba(255, 255, 255, 0.3);
  }
  to {
    box-shadow: 
      0 15px 40px rgba(0, 0, 0, 0.4),
      0 0 80px rgba(255, 215, 0, 0.6),
      inset 0 2px 15px rgba(255, 255, 255, 0.5);
  }
}

@keyframes wingFlap {
  from {
    transform: translateY(-50%) rotate(-15deg) scale(1);
  }
  to {
    transform: translateY(-50%) rotate(-25deg) scale(1.1);
  }
}

.wing-right {
  animation-name: wingFlapRight;
}

@keyframes wingFlapRight {
  from {
    transform: translateY(-50%) rotate(15deg) scale(1);
  }
  to {
    transform: translateY(-50%) rotate(25deg) scale(1.1);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
}

/* Mobile responsive */
@media (max-width: 768px) {
  .logo-text {
    font-size: 2.5rem;
    letter-spacing: 2px;
  }
  
  .logo {
    padding: 15px 30px;
  }
  
  .logo-wings {
    width: 160px;
    height: 60px;
  }
  
  .wing {
    width: 50px;
    height: 25px;
  }
  
  .wing-left {
    left: -25px;
  }
  
  .wing-right {
    right: -25px;
  }
  
  .loading-spinner {
    width: 60px;
    height: 60px;
  }
  
  .loading-text {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .logo-text {
    font-size: 2rem;
    letter-spacing: 1px;
  }
  
  .logo {
    padding: 12px 25px;
  }
  
  .logo-wings {
    width: 140px;
    height: 50px;
  }
  
  .wing {
    width: 40px;
    height: 20px;
  }
  
  .wing-left {
    left: -20px;
  }
  
  .wing-right {
    right: -20px;
  }
}
