.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  overflow: hidden;
}

.background-svg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.background-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  animation: fadeIn 0.8s ease-in-out;
  position: relative;
  z-index: 2;
}

.logo-container {
  margin-bottom: 40px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-svg {
  max-width: 300px;
  width: 80vw;
  height: auto;
  filter: drop-shadow(0 10px 30px rgba(0, 0, 0, 0.3))
          drop-shadow(0 0 50px rgba(255, 215, 0, 0.3));
  animation: logoGlow 2s ease-in-out infinite alternate;
}

.loading-spinner {
  position: relative;
  width: 80px;
  height: 80px;
  margin-bottom: 30px;
}

.spinner-ring {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 4px solid transparent;
  border-top: 4px solid #FFD700;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.spinner-ring:nth-child(2) {
  width: 60px;
  height: 60px;
  top: 10px;
  left: 10px;
  border-top-color: #FFA500;
  animation-duration: 1.5s;
  animation-direction: reverse;
}

.spinner-ring:nth-child(3) {
  width: 40px;
  height: 40px;
  top: 20px;
  left: 20px;
  border-top-color: #FF8C00;
  animation-duration: 2s;
}

.loading-text {
  color: white;
  font-size: 1.2rem;
  font-weight: 500;
  opacity: 0.9;
  animation: pulse 2s ease-in-out infinite;
}

.loading-text p {
  margin: 0;
  letter-spacing: 1px;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes logoGlow {
  from {
    filter: drop-shadow(0 10px 30px rgba(0, 0, 0, 0.3))
            drop-shadow(0 0 50px rgba(255, 215, 0, 0.3));
    transform: scale(1);
  }
  to {
    filter: drop-shadow(0 15px 40px rgba(0, 0, 0, 0.4))
            drop-shadow(0 0 80px rgba(255, 215, 0, 0.6));
    transform: scale(1.05);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
}

/* Mobile responsive */
@media (max-width: 768px) {
  .logo-svg {
    max-width: 250px;
    width: 85vw;
  }

  .loading-spinner {
    width: 60px;
    height: 60px;
  }

  .loading-text {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .logo-svg {
    max-width: 200px;
    width: 90vw;
  }

  .loading-spinner {
    width: 50px;
    height: 50px;
  }

  .spinner-ring:nth-child(2) {
    width: 37.5px;
    height: 37.5px;
    top: 6.25px;
    left: 6.25px;
  }

  .spinner-ring:nth-child(3) {
    width: 25px;
    height: 25px;
    top: 12.5px;
    left: 12.5px;
  }

  .loading-text {
    font-size: 0.9rem;
  }
}
