@echo off
echo ========================================
echo    SETUP GIT REPOSITORY
echo ========================================
echo.

echo Checking Git installation...
git --version
if %errorlevel% neq 0 (
    echo ERROR: Git is not installed or not in PATH
    echo Please install Git from: https://git-scm.com/download/windows
    echo Or add Git to your PATH environment variable
    pause
    exit /b 1
)

echo.
echo Initializing Git repository...
git init

echo.
echo Adding all files to staging...
git add .

echo.
echo Creating initial commit...
git commit -m "Initial commit: React Cloudflare Stream Livestream Viewer

Features:
- View live and ready videos from Cloudflare Stream
- Responsive grid layout
- Auto-refresh every 30 seconds
- Toggle between Live & Ready and All videos
- Modern UI with animations
- Single-page application ready for deployment"

echo.
echo ========================================
echo    NEXT STEPS:
echo ========================================
echo 1. Create a new repository on GitHub:
echo    https://github.com/new
echo.
echo 2. Name it: cloudflare-livestream-viewer
echo.
echo 3. Run these commands (replace YOUR_USERNAME):
echo    git remote add origin https://github.com/YOUR_USERNAME/cloudflare-livestream-viewer.git
echo    git branch -M main
echo    git push -u origin main
echo.
echo ========================================

pause
