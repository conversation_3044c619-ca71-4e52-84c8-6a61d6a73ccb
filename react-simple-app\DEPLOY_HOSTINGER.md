# 🚀 Deploy React Livestream App lên Hostinger

## 📋 Y<PERSON>u cầu
- <PERSON><PERSON><PERSON> k<PERSON>n Hostinger với hosting plan
- Domain đã trỏ về hosting

## 🎯 Cách 1: Deploy nhanh (Khuyến nghị)

### Bước 1: Chu<PERSON>n bị file
1. <PERSON><PERSON><PERSON> tên `demo.html` thành `index.html`
2. File này đã chứa tất cả code cần thiết

### Bước 2: Upload lên Hostinger
1. **Đăng nhập** Hostinger Control Panel
2. **Mở File Manager** (hoặc dùng FTP client)
3. **Vào thư mục** `public_html`
4. **Upload** file `index.html`
5. **Truy cập** domain của bạn

## 🛠️ Cách 2: Build và deploy

### Bước 1: Build project
```bash
# Chạy file build.bat
build.bat
```

### Bước 2: Upload files
1. Upload tất cả files trong thư mục `build/` lên `public_html/`

## 🔧 <PERSON><PERSON><PERSON> hình Hostinger

### 1. File Manager Upload:
- Vào **Hostinger Control Panel**
- Chọn **File Manager**
- Navigate đến `public_html`
- Upload `index.html`

### 2. FTP Upload (Nâng cao):
```
Host: ftp.yourdomain.com
Username: [your-ftp-username]
Password: [your-ftp-password]
Port: 21
```

### 3. Kiểm tra CORS (nếu cần):
Tạo file `.htaccess` trong `public_html`:
```apache
# Enable CORS for API calls
Header always set Access-Control-Allow-Origin "*"
Header always set Access-Control-Allow-Methods "GET, POST, OPTIONS"
Header always set Access-Control-Allow-Headers "Content-Type, Authorization"

# Handle preflight requests
RewriteEngine On
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ $1 [R=200,L]
```

## 🌐 Truy cập ứng dụng

Sau khi upload thành công:
- **URL**: `https://yourdomain.com`
- **Tính năng**: Xem livestream từ Cloudflare Stream
- **Auto-refresh**: Cập nhật danh sách video mỗi 30 giây

## 🔍 Troubleshooting

### Lỗi CORS:
- Thêm file `.htaccess` như hướng dẫn trên
- Hoặc liên hệ Hostinger support để enable CORS

### Lỗi API:
- Kiểm tra Cloudflare credentials trong code
- Đảm bảo API token có quyền truy cập Stream

### Lỗi hiển thị:
- Kiểm tra file `index.html` đã upload đúng
- Clear browser cache và refresh

## 📱 Responsive Design
Ứng dụng tự động điều chỉnh theo màn hình:
- **Desktop**: Grid 2-3 cột
- **Tablet**: Grid 2 cột  
- **Mobile**: Grid 1 cột

## 🔄 Cập nhật
Để cập nhật ứng dụng:
1. Sửa code trong `demo.html`
2. Upload lại file `index.html` lên Hostinger
3. Clear cache và refresh browser

## 📞 Hỗ trợ
- **Hostinger Support**: Liên hệ qua control panel
- **Documentation**: https://support.hostinger.com
