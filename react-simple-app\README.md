# React Simple App

Một ứng dụng React đơn giản được tạo để demo các tính năng cơ bản của React.

## Tính năng

- ✅ Bộ đếm với useState Hook
- ✅ Form input với state management
- ✅ CSS styling với gradient và animations
- ✅ Responsive design
- ✅ Modern React với Hooks

## Cách chạy ứng dụng

1. Cài đặt dependencies:
```bash
cd react-simple-app
npm install
```

2. Chạy ứng dụng:
```bash
npm start
```

3. Mở trình duyệt và truy cập: `http://localhost:3000`

## Cấu trúc project

```
react-simple-app/
├── public/
│   └── index.html          # HTML template
├── src/
│   ├── App.js             # Component chính
│   ├── App.css            # Styles cho App
│   ├── index.js           # Entry point
│   └── index.css          # Global styles
├── package.json           # Dependencies và scripts
└── README.md             # Tài liệu này
```

## Scripts có sẵn

- `npm start` - Chạy ứng dụng ở chế độ development
- `npm run build` - Build ứng dụng cho production
- `npm test` - Chạy tests
- `npm run eject` - Eject cấu hình (không khuyến khích)

## Công nghệ sử dụng

- React 18
- React DOM
- React Scripts
- CSS3 với Flexbox và Grid
- ES6+ JavaScript
