import React, { useState, useEffect } from 'react';
import './App.css';
import LivestreamViewer from './components/LivestreamViewer';
import LoadingScreen from './components/LoadingScreen';

function App() {
  const [currentView, setCurrentView] = useState('home');
  const [count, setCount] = useState(0);
  const [name, setName] = useState('');
  const [isLoading, setIsLoading] = useState(true);

  // Simulate loading time
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 3000); // 3 seconds loading time

    return () => clearTimeout(timer);
  }, []);

  // Show loading screen
  if (isLoading) {
    return <LoadingScreen />;
  }

  const renderView = () => {
    switch(currentView) {
      case 'livestream':
        return <LivestreamViewer />;
      case 'about':
        return (
          <>
            <h1>ℹ️ Giới thiệu</h1>
            <p>STOP Mobile App - Ứng dụng xem livestream được tối ưu cho mobile.</p>

            <div className="info-section">
              <h2>🎯 Tính năng chính</h2>
              <ul>
                <li>📺 Xem livestream từ Cloudflare Stream</li>
                <li>📱 Giao diện mobile responsive</li>
                <li>🎨 Loading screen với SVG đẹp mắt</li>
                <li>⚡ Cập nhật real-time</li>
                <li>🎮 Điều khiển touch-friendly</li>
                <li>🔄 Auto-refresh mỗi 30 giây</li>
              </ul>
            </div>

            <div className="info-section">
              <h2>🛠️ Công nghệ</h2>
              <ul>
                <li>⚛️ React.js</li>
                <li>☁️ Cloudflare Stream API</li>
                <li>🎨 CSS3 Animations</li>
                <li>📱 Mobile-first Design</li>
                <li>🖼️ SVG Graphics</li>
              </ul>
              <p><strong>Phiên bản:</strong> 2.0.0</p>
            </div>
          </>
        );
      case 'home':
      default:
        return (
          <>
            <h1>🚀 STOP Mobile App</h1>
            <p>Chào mừng bạn đến với ứng dụng livestream mobile!</p>

            <div className="counter-section">
              <h2>🔢 Bộ đếm: {count}</h2>
              <div className="button-group">
                <button onClick={() => setCount(count + 1)}>
                  ➕ Tăng
                </button>
                <button onClick={() => setCount(count - 1)}>
                  ➖ Giảm
                </button>
                <button onClick={() => setCount(0)}>
                  🔄 Reset
                </button>
              </div>
            </div>

            <div className="input-section">
              <h2>📝 Xin chào, {name || 'Bạn'}!</h2>
              <input
                type="text"
                placeholder="Nhập tên của bạn..."
                value={name}
                onChange={(e) => setName(e.target.value)}
              />
              {name && <p>Chào {name}! 👋 Hãy thử xem livestream nhé!</p>}
            </div>

            <div className="info-section">
              <h2>🎯 Tính năng ứng dụng</h2>
              <ul>
                <li>🔴 Xem livestream Cloudflare Stream</li>
                <li>📱 Mobile responsive design</li>
                <li>🎨 Loading screen với SVG</li>
                <li>⚡ Real-time updates</li>
                <li>🎮 Touch-friendly interface</li>
                <li>🔄 Auto-refresh streams</li>
              </ul>
              <p>👆 Nhấn tab <strong>Livestream</strong> để xem video trực tiếp!</p>
            </div>
          </>
        );
    }
  };

  return (
    <div className="App">
      <nav className="App-nav">
        <button
          className={currentView === 'home' ? 'active' : ''}
          onClick={() => setCurrentView('home')}
        >
          🏠 Trang chủ
        </button>
        <button
          className={currentView === 'livestream' ? 'active' : ''}
          onClick={() => setCurrentView('livestream')}
        >
          🔴 Livestream
        </button>
        <button
          className={currentView === 'about' ? 'active' : ''}
          onClick={() => setCurrentView('about')}
        >
          ℹ️ Giới thiệu
        </button>
      </nav>

      <header className="App-header">
        {renderView()}
      </header>
    </div>
  );
}

export default App;
