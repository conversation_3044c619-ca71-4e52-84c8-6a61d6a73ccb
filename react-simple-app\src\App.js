import React, { useState, useEffect } from 'react';
import './App.css';
import LivestreamViewer from './components/LivestreamViewer';
import LoadingScreen from './components/LoadingScreen';

function App() {
  const [currentView, setCurrentView] = useState('home');
  const [count, setCount] = useState(0);
  const [name, setName] = useState('');
  const [isLoading, setIsLoading] = useState(true);

  // Simulate loading time
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 3000); // 3 seconds loading time

    return () => clearTimeout(timer);
  }, []);

  // Show loading screen
  if (isLoading) {
    return <LoadingScreen />;
  }

  const renderView = () => {
    switch(currentView) {
      case 'livestream':
        return <LivestreamViewer />;
      case 'home':
      default:
        return (
          <>
            <h1>🚀 React Simple App</h1>
            <p>Chào mừng bạn đến với ứng dụng React đơn giản!</p>

            <div className="counter-section">
              <h2>Bộ đếm: {count}</h2>
              <div className="button-group">
                <button onClick={() => setCount(count + 1)}>
                  Tăng (+)
                </button>
                <button onClick={() => setCount(count - 1)}>
                  Giảm (-)
                </button>
                <button onClick={() => setCount(0)}>
                  Reset
                </button>
              </div>
            </div>

            <div className="input-section">
              <h2>Xin chào, {name || 'Bạn'}!</h2>
              <input
                type="text"
                placeholder="Nhập tên của bạn..."
                value={name}
                onChange={(e) => setName(e.target.value)}
              />
            </div>

            <div className="info-section">
              <p>
                Đây là một ứng dụng React đơn giản với:
              </p>
              <ul>
                <li>✅ Bộ đếm với useState</li>
                <li>✅ Input form với state</li>
                <li>✅ CSS styling</li>
                <li>✅ Responsive design</li>
                <li>🔴 Xem Livestream với Cloudflare</li>
              </ul>
            </div>
          </>
        );
    }
  };

  return (
    <div className="App">
      <nav className="App-nav">
        <button
          className={currentView === 'home' ? 'active' : ''}
          onClick={() => setCurrentView('home')}
        >
          🏠 Trang chủ
        </button>
        <button
          className={currentView === 'livestream' ? 'active' : ''}
          onClick={() => setCurrentView('livestream')}
        >
          🔴 Livestream
        </button>
      </nav>

      <header className="App-header">
        {renderView()}
      </header>
    </div>
  );
}

export default App;
