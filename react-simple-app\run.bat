@echo off
echo 🚀 Starting STOP Mobile App Server...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Python found! Starting server on port 8000...
    echo 📱 Open: http://localhost:8000/index-production.html
    echo 🔴 Livestream: http://localhost:8000/livestream-demo.html
    echo.
    echo Press Ctrl+C to stop server
    echo.
    python -m http.server 8000
) else (
    echo ❌ Python not found!
    echo.
    echo 💡 Alternative: Double-click index-production.html to open in browser
    echo.
    pause
)
