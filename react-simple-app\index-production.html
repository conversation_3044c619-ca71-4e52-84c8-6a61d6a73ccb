<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <meta name="theme-color" content="#667eea">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="mobile-web-app-capable" content="yes">
    <title>STOP - Mobile Livestream App</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        html {
            font-size: 16px;
            width: 100%;
            height: 100%;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            min-height: 100vh;
            overflow-x: hidden;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }

        #root {
            width: 100%;
            min-height: 100vh;
        }

        /* Loading Screen */
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            animation: fadeOut 1s ease-in-out 3s forwards;
        }

        .background-svg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }

        .background-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .loading-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            animation: fadeIn 0.8s ease-in-out;
            position: relative;
            z-index: 2;
        }

        .logo-container {
            margin-bottom: 40px;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .logo-svg {
            max-width: 300px;
            width: 80vw;
            height: auto;
            filter: drop-shadow(0 10px 30px rgba(0, 0, 0, 0.3))
                    drop-shadow(0 0 50px rgba(255, 215, 0, 0.3));
            animation: logoGlow 2s ease-in-out infinite alternate;
        }

        .loading-spinner {
            position: relative;
            width: 80px;
            height: 80px;
            margin-bottom: 30px;
        }

        .spinner-ring {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: 4px solid transparent;
            border-top: 4px solid #FFD700;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        .spinner-ring:nth-child(2) {
            width: 60px;
            height: 60px;
            top: 10px;
            left: 10px;
            border-top-color: #FFA500;
            animation-duration: 1.5s;
            animation-direction: reverse;
        }

        .spinner-ring:nth-child(3) {
            width: 40px;
            height: 40px;
            top: 20px;
            left: 20px;
            border-top-color: #FF8C00;
            animation-duration: 2s;
        }

        .loading-text {
            color: white;
            font-size: 1.2rem;
            font-weight: 500;
            opacity: 0.9;
            animation: pulse 2s ease-in-out infinite;
        }

        .loading-text p {
            margin: 0;
            letter-spacing: 1px;
        }

        /* Main App */
        .main-app {
            opacity: 0;
            animation: fadeIn 1s ease-in-out 4s forwards;
            text-align: center;
            min-height: 100vh;
            width: 100%;
            overflow-x: hidden;
        }

        .app-nav {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px 20px;
            display: flex;
            justify-content: center;
            gap: 20px;
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: sticky;
            top: 0;
            z-index: 100;
            width: 100%;
            box-sizing: border-box;
        }

        .nav-button {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.2);
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: bold;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            min-width: 140px;
        }

        .nav-button:hover, .nav-button.active {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            border-color: #ff6b6b;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }

        .app-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px 15px;
            color: white;
            min-height: calc(100vh - 70px);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: flex-start;
            font-size: calc(10px + 2vmin);
            width: 100%;
            box-sizing: border-box;
            padding-top: 30px;
        }

        .content-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin: 20px 0;
            max-width: 90%;
        }

        .content-section h1 {
            font-size: 2rem;
            margin-bottom: 15px;
        }

        .content-section h2 {
            font-size: 1.5rem;
            margin-bottom: 15px;
        }

        .content-section p {
            font-size: 1.1rem;
            margin-bottom: 15px;
        }

        .content-section ul {
            text-align: left;
            max-width: 300px;
            margin: 15px auto;
        }

        .content-section li {
            font-size: 1rem;
            padding: 8px 0;
        }

        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeOut {
            to { opacity: 0; visibility: hidden; }
        }

        @keyframes logoGlow {
            from {
                filter: drop-shadow(0 10px 30px rgba(0, 0, 0, 0.3))
                        drop-shadow(0 0 50px rgba(255, 215, 0, 0.3));
                transform: scale(1);
            }
            to {
                filter: drop-shadow(0 15px 40px rgba(0, 0, 0, 0.4))
                        drop-shadow(0 0 80px rgba(255, 215, 0, 0.6));
                transform: scale(1.05);
            }
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.7; }
            50% { opacity: 1; }
        }

        /* Mobile responsive */
        @media screen and (max-width: 768px) {
            html { font-size: 14px; }
            
            .app-nav {
                padding: 12px 15px;
                gap: 15px;
                flex-wrap: wrap;
            }
            
            .nav-button {
                padding: 8px 16px;
                font-size: 0.9rem;
                min-width: 120px;
            }
            
            .app-header {
                padding: 15px 10px;
                font-size: calc(8px + 2vmin);
                min-height: calc(100vh - 60px);
                padding-top: 20px;
            }
            
            .content-section {
                margin: 15px 0;
                padding: 15px;
                max-width: 95%;
            }
            
            .content-section h1 { font-size: 1.8rem; }
            .content-section h2 { font-size: 1.4rem; }
            .content-section p { font-size: 1rem; }
            
            .logo-svg {
                max-width: 250px;
                width: 85vw;
            }
            
            .loading-spinner {
                width: 60px;
                height: 60px;
            }
            
            .loading-text { font-size: 1rem; }
        }

        @media screen and (max-width: 480px) {
            html { font-size: 13px; }
            
            .nav-button {
                padding: 6px 12px;
                font-size: 0.8rem;
                min-width: 100px;
            }
            
            .app-header {
                padding: 10px 8px;
                padding-top: 15px;
            }
            
            .content-section {
                padding: 12px;
                max-width: 98%;
            }
            
            .content-section h1 { font-size: 1.5rem; }
            .content-section h2 { font-size: 1.2rem; }
            .content-section p { font-size: 0.9rem; }
            
            .logo-svg {
                max-width: 200px;
                width: 90vw;
            }
            
            .loading-spinner {
                width: 50px;
                height: 50px;
            }
            
            .spinner-ring:nth-child(2) {
                width: 37.5px;
                height: 37.5px;
                top: 6.25px;
                left: 6.25px;
            }
            
            .spinner-ring:nth-child(3) {
                width: 25px;
                height: 25px;
                top: 12.5px;
                left: 12.5px;
            }
            
            .loading-text { font-size: 0.9rem; }
        }

        /* Prevent horizontal scroll */
        @media screen and (max-width: 768px) {
            body { overflow-x: hidden; }
        }
    </style>
</head>
<body>
    <div id="root">
        <!-- Loading Screen -->
        <div class="loading-screen">
            <!-- Background SVG -->
            <div class="background-svg">
                <img src="images/background.svg" alt="Background" class="background-image">
            </div>
            
            <div class="loading-content">
                <div class="logo-container">
                    <!-- Logo SVG -->
                    <img src="images/logo.svg" alt="STOP Logo" class="logo-svg">
                </div>
                
                <div class="loading-spinner">
                    <div class="spinner-ring"></div>
                    <div class="spinner-ring"></div>
                    <div class="spinner-ring"></div>
                </div>
                
                <div class="loading-text">
                    <p>Đang tải...</p>
                </div>
            </div>
        </div>

        <!-- Main App -->
        <div class="main-app">
            <nav class="app-nav">
                <button class="nav-button active" onclick="showHome()">🏠 Trang chủ</button>
                <button class="nav-button" onclick="showLivestream()">🔴 Livestream</button>
                <button class="nav-button" onclick="showAbout()">ℹ️ Giới thiệu</button>
            </nav>

            <div class="app-header">
                <div id="home-content" class="content-section">
                    <h1>🚀 STOP Mobile App</h1>
                    <p>Chào mừng bạn đến với ứng dụng livestream mobile!</p>
                    <p>Ứng dụng đã được tối ưu hóa hoàn toàn cho thiết bị di động với giao diện đẹp mắt và hiệu suất cao.</p>
                </div>

                <div id="mobile-features" class="content-section">
                    <h2>📱 Tính năng Mobile</h2>
                    <p>Giao diện đã được tối ưu hóa cho thiết bị di động với:</p>
                    <ul>
                        <li>✅ Responsive design hoàn toàn</li>
                        <li>✅ Touch-friendly buttons</li>
                        <li>✅ Mobile viewport tối ưu</li>
                        <li>✅ Loading screen với SVG đẹp mắt</li>
                        <li>✅ Hiệu ứng animations mượt mà</li>
                        <li>✅ Sticky navigation</li>
                        <li>✅ Tối ưu cho mọi kích thước màn hình</li>
                    </ul>
                </div>

                <div id="livestream-content" class="content-section" style="display: none;">
                    <h2>🔴 Livestream</h2>
                    <p>Kết nối với Cloudflare Stream để xem video trực tiếp...</p>

                    <div class="livestream-info">
                        <h3>📡 Trạng thái kết nối</h3>
                        <p id="connection-status">🔄 Đang kết nối...</p>
                        <button onclick="connectToStream()" class="nav-button">🔗 Kết nối Livestream</button>
                    </div>

                    <div id="video-container" style="display: none;">
                        <h3>📺 Video Player</h3>
                        <div class="video-wrapper" style="position: relative; width: 100%; height: 300px; background: #000; border-radius: 10px; margin: 20px 0;">
                            <p style="color: white; text-align: center; padding-top: 130px;">
                                🎥 Video sẽ hiển thị ở đây khi có livestream
                            </p>
                        </div>
                    </div>

                    <div class="livestream-features">
                        <h3>✨ Tính năng</h3>
                        <ul>
                            <li>📺 Xem livestream chất lượng cao</li>
                            <li>🔄 Auto-refresh mỗi 30 giây</li>
                            <li>📱 Tối ưu cho mobile</li>
                            <li>⚡ Real-time updates</li>
                        </ul>
                    </div>
                </div>

                <div id="about-content" class="content-section" style="display: none;">
                    <h2>ℹ️ Giới thiệu</h2>
                    <p>STOP Mobile App được phát triển với công nghệ hiện đại:</p>
                    <ul>
                        <li>⚛️ React.js</li>
                        <li>📱 Mobile-first design</li>
                        <li>🎨 SVG graphics</li>
                        <li>✨ CSS3 animations</li>
                        <li>📐 Responsive layout</li>
                    </ul>
                    <p>Phiên bản: 1.0.0</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Navigation functionality
        function showHome() {
            hideAllContent();
            document.getElementById('home-content').style.display = 'block';
            document.getElementById('mobile-features').style.display = 'block';
            setActiveButton(0);
        }

        function showLivestream() {
            hideAllContent();
            document.getElementById('livestream-content').style.display = 'block';
            setActiveButton(1);
        }

        function showAbout() {
            hideAllContent();
            document.getElementById('about-content').style.display = 'block';
            setActiveButton(2);
        }

        function hideAllContent() {
            document.getElementById('home-content').style.display = 'none';
            document.getElementById('mobile-features').style.display = 'none';
            document.getElementById('livestream-content').style.display = 'none';
            document.getElementById('about-content').style.display = 'none';
        }

        function setActiveButton(index) {
            const buttons = document.querySelectorAll('.nav-button');
            buttons.forEach(btn => btn.classList.remove('active'));
            buttons[index].classList.add('active');
        }

        // Livestream functionality
        function connectToStream() {
            const statusElement = document.getElementById('connection-status');
            const videoContainer = document.getElementById('video-container');

            statusElement.innerHTML = '🔄 Đang kết nối với Cloudflare Stream...';

            // Simulate connection process
            setTimeout(function() {
                statusElement.innerHTML = '✅ Đã kết nối! Đang tìm livestream...';

                setTimeout(function() {
                    statusElement.innerHTML = '📺 Sẵn sàng xem livestream';
                    videoContainer.style.display = 'block';

                    // Show demo message
                    setTimeout(function() {
                        statusElement.innerHTML = '⚠️ Demo mode - Cần API key thật để xem livestream';
                    }, 2000);
                }, 1500);
            }, 1000);
        }

        // Mobile optimizations
        document.addEventListener('DOMContentLoaded', function() {
            // Prevent zoom on input focus (iOS)
            document.addEventListener('touchstart', function() {}, true);

            // Handle orientation change
            window.addEventListener('orientationchange', function() {
                setTimeout(function() {
                    window.scrollTo(0, 0);
                }, 100);
            });
        });
    </script>
</body>
</html>
